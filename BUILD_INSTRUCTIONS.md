# تعليمات بناء تطبيق بالشفا للإنتاج

## 📱 إعداد التطبيق للنشر

### 🔧 الخطوة 1: التحقق من الإعدادات

1. **تحديث رقم الإصدار في pubspec.yaml:**
   ```yaml
   version: 1.0.0+1  # زد الرقم مع كل تحديث
   ```

2. **التحقق من الأذونات في AndroidManifest.xml:**
   - ✅ أذونات الإشعارات
   - ✅ أذونات الاهتزاز
   - ✅ أذونات التشغيل في الخلفية

### 🏗️ الخطوة 2: بناء APK للإنتاج

#### بناء APK عادي (للتوزيع المباشر):
```bash
flutter clean
flutter pub get
flutter build apk --release
```

#### بناء APK مقسم حسب المعمارية (حجم أصغر):
```bash
flutter build apk --split-per-abi --release
```

#### بناء App Bundle (للنشر على Google Play):
```bash
flutter build appbundle --release
```

### 📁 مواقع الملفات المبنية:

- **APK عادي:** `build/app/outputs/flutter-apk/app-release.apk`
- **APK مقسم:** `build/app/outputs/flutter-apk/app-arm64-v8a-release.apk`
- **App Bundle:** `build/app/outputs/bundle/release/app-release.aab`

### 🔍 الخطوة 3: اختبار التطبيق

1. **تثبيت APK على جهاز حقيقي:**
   ```bash
   adb install build/app/outputs/flutter-apk/app-release.apk
   ```

2. **اختبار الوظائف الأساسية:**
   - ✅ إضافة مريض جديد
   - ✅ إضافة دواء
   - ✅ جدولة الجرعات
   - ✅ الإشعارات
   - ✅ تأكيد الجرعات

### 📊 معلومات التطبيق:

- **الاسم:** بالشفا
- **الحزمة:** com.example.balshifa
- **الإصدار:** 1.0.0+1
- **الحد الأدنى لأندرويد:** API 21 (Android 5.0)
- **الهدف:** API 34 (Android 14)

### 🎯 الميزات المتضمنة:

- ✅ إدارة المرضى
- ✅ إدارة الأدوية
- ✅ جدولة الجرعات التلقائية
- ✅ إشعارات التذكير
- ✅ تأكيد أخذ الجرعات
- ✅ سجل الجرعات
- ✅ تعديل الأدوية
- ✅ قاعدة بيانات محلية
- ✅ واجهة عربية

### 🔧 استكشاف الأخطاء:

#### إذا فشل البناء:
1. تنظيف المشروع: `flutter clean`
2. إعادة تحميل التبعيات: `flutter pub get`
3. التحقق من أخطاء الكود: `flutter analyze`
4. إعادة المحاولة

#### إذا لم تعمل الإشعارات:
1. التحقق من أذونات الإشعارات في الجهاز
2. التأكد من وجود ملف الصوت في `android/app/src/main/res/raw/`
3. إعادة تثبيت التطبيق

### 📝 ملاحظات مهمة:

1. **للنشر على Google Play:**
   - استخدم App Bundle بدلاً من APK
   - وقع التطبيق بمفتاح إنتاج
   - اتبع سياسات Google Play

2. **للتوزيع المباشر:**
   - استخدم APK مقسم لتوفير مساحة
   - تأكد من تفعيل "مصادر غير معروفة" في الجهاز

3. **الأمان:**
   - لا تشارك مفاتيح التوقيع
   - احتفظ بنسخة احتياطية من keystore
   - استخدم ProGuard للحماية

### 🚀 خطوات النشر:

1. **اختبار شامل** على أجهزة مختلفة
2. **تحديث رقم الإصدار**
3. **بناء الملف النهائي**
4. **توقيع التطبيق** (للنشر الرسمي)
5. **رفع على المتجر** أو توزيع مباشر

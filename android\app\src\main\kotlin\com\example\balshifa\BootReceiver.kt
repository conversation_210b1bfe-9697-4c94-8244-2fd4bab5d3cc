package com.example.balshifa

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel

class BootReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            "android.intent.action.QUICKBOOT_POWERON",
            "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                Log.d("BootReceiver", "تم استلام إشعار إعادة التشغيل")
                
                // إعادة جدولة جميع المنبهات
                rescheduleAlarms(context)
            }
        }
    }
    
    private fun rescheduleAlarms(context: Context) {
        try {
            // إرسال intent للتطبيق الرئيسي لإعادة جدولة المنبهات
            val rescheduleIntent = Intent(context, MainActivity::class.java).apply {
                action = "RESCHEDULE_ALARMS"
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(rescheduleIntent)
            
            Log.d("BootReceiver", "تم إرسال طلب إعادة جدولة المنبهات")
        } catch (e: Exception) {
            Log.e("BootReceiver", "خطأ في إعادة جدولة المنبهات: ${e.message}")
        }
    }
}

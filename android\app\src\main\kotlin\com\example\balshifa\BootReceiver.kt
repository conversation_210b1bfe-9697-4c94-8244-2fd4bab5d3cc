package com.example.balshifa

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class BootReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            "android.intent.action.QUICKBOOT_POWERON",
            "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                Log.d("BootReceiver", "🔄 تم إعادة تشغيل الجهاز - إعادة جدولة المنبهات")
                
                // بدء MainActivity لإعادة جدولة المنبهات
                val mainIntent = Intent(context, MainActivity::class.java).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    putExtra("RESCHEDULE_ALARMS", true)
                }
                
                try {
                    context.startActivity(mainIntent)
                    Log.d("BootR<PERSON>eiver", "✅ تم بدء MainActivity لإعادة الجدولة")
                } catch (e: Exception) {
                    Log.e("BootReceiver", "❌ خطأ في بدء MainActivity: ${e.message}")
                }
            }
        }
    }
}

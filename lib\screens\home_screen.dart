import 'package:balshifa/models/patient.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/models/medicine.dart';
import 'package:balshifa/screens/patient_screen.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/services/alarm_service.dart';
import 'package:balshifa/widgets/patient_tile.dart';
import 'package:balshifa/constants/app_colors.dart';
import 'package:balshifa/constants/app_styles.dart';
import 'package:flutter/material.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<Patient> patients = [];

  @override
  void initState() {
    super.initState();
    _loadPatients();
  }

  // إعادة جدولة جميع المنبهات
  _rescheduleAlarms() async {
    try {
      print('🔄 بدء إعادة جدولة المنبهات...');

      // إعادة جدولة جميع المنبهات
      await AlarmService().scheduleAllDoseAlarms();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إعادة جدولة جميع المنبهات! 🔄\nتحقق من وحدة التحكم للتفاصيل'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      print('❌ خطأ في إعادة جدولة المنبهات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة جدولة المنبهات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // طلب أذونات النظام
  _requestPermissions() async {
    try {
      print('🔐 بدء طلب أذونات النظام...');

      await AlarmService().requestSpecialPermissions();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فتح إعدادات الأذونات! 🔐\nيرجى منح الأذونات المطلوبة'),
            backgroundColor: Colors.purple,
            duration: Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      print('❌ خطأ في طلب الأذونات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في طلب الأذونات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تم إزالة NotificationService القديم
  }

  _loadPatients() async {
    final loadedPatients = await DatabaseService.instance.getAllPatients();
    setState(() {
      patients = loadedPatients;
    });
  }

  _addPatient() async {
    String patientName = '';
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.person_add,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'إضافة مريض جديد',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.lightGreen,
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            onChanged: (value) => patientName = value,
            decoration: InputDecoration(
              hintText: 'اسم المريض',
              hintStyle: TextStyle(color: AppColors.textHint),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
              prefixIcon: Icon(Icons.person, color: AppColors.primary),
            ),
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextButton.icon(
              onPressed: () async {
                if (patientName.trim().isNotEmpty) {
                  final patient = Patient(name: patientName.trim());
                  await DatabaseService.instance.insertPatient(patient);
                  _loadPatients();
                  Navigator.pop(context);

                  // عرض رسالة نجاح
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم إضافة المريض "${patientName.trim()}" بنجاح! ✅'),
                        backgroundColor: AppColors.success,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    );
                  }
                }
              },
              icon: const Icon(Icons.save, color: Colors.white),
              label: const Text(
                'حفظ',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // اختبار المنبه
  _testAlarm() async {
    try {
      print('🧪 بدء اختبار المنبه...');

      final db = DatabaseService.instance;

      // التحقق من وجود مريض، وإنشاء واحد إذا لم يوجد
      var patients = await db.getAllPatients();
      if (patients.isEmpty) {
        final testPatient = Patient(
          name: 'مريض تجريبي للمنبه',
        );
        await db.insertPatient(testPatient);
        patients = await db.getAllPatients();
        print('✅ تم إنشاء مريض تجريبي');
      }

      // إنشاء دواء تجريبي
      final testMedicine = Medicine(
        patientId: patients.first.id!,
        name: 'دواء تجريبي للمنبه',
        type: 'مسكن',
        dosage: '1 حبة',
        timesPerDay: 1,
        durationDays: 1,
        firstDoseDateTime: DateTime.now().add(const Duration(seconds: 5)),
      );

      final medicineId = await db.insertMedicine(testMedicine);
      print('✅ تم إنشاء دواء تجريبي');

      // إنشاء جرعة تجريبية
      final testDose = Dose(
        medicineId: medicineId,
        scheduledDateTime: DateTime.now().add(const Duration(seconds: 5)),
        status: 'scheduled',
      );

      await db.insertDose(testDose);
      print('✅ تم حفظ الجرعة التجريبية');

      // جدولة منبه تجريبي مباشرة
      await AlarmService().scheduleAlarmForDose(
        testDose.id ?? 999,
        testMedicine.name,
        testDose.scheduledDateTime,
      );
      print('✅ تم جدولة المنبه التجريبي مباشرة');
    } catch (e) {
      print('❌ خطأ في اختبار المنبه: $e');
    }

    // عرض رسالة تأكيد عصرية
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.alarm,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'تم جدولة منبه تجريبي! ⏰\nسيظهر خلال 5 ثوان',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: const EdgeInsets.all(16),
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  // اختيار صوت المنبه
  _selectAlarmSound() {
    print('🔊 فتح نافذة اختيار الصوت');

    final availableSounds = [
      {'id': 'alarm_default', 'name': 'منبه افتراضي', 'description': 'صوت منبه عادي'},
      {'id': 'alarm_classic', 'name': 'منبه كلاسيكي', 'description': 'صوت منبه تقليدي'},
      {'id': 'alarm_gentle', 'name': 'منبه هادئ', 'description': 'صوت لطيف وهادئ'},
      {'id': 'alarm_loud', 'name': 'منبه قوي', 'description': 'صوت عالي وواضح'},
      {'id': 'alarm_beep', 'name': 'صفارة قصيرة', 'description': 'صوت صفارة متكررة'},
      {'id': 'alarm_emergency', 'name': 'منبه طوارئ', 'description': 'صوت طوارئ قوي'},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار صوت المنبه 🔊'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: availableSounds.map((sound) {
              final isSelected = AlarmService().selectedSound == sound['id'];
              return Card(
                margin: const EdgeInsets.symmetric(vertical: 4),
                child: ListTile(
                  title: Text(sound['name']!),
                  subtitle: Text(sound['description']!),
                  leading: Icon(
                    isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                    color: isSelected ? AppColors.primary : Colors.grey,
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.play_arrow, color: AppColors.primary),
                    onPressed: () async {
                      print('🔊 معاينة الصوت: ${sound['name']}');
                      await AlarmService().previewSound(sound['id']!);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('تشغيل معاينة: ${sound['name']} 🔊'),
                          backgroundColor: AppColors.primary,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                  ),
                  onTap: () {
                    print('🎵 تم اختيار الصوت: ${sound['name']}');
                    AlarmService().setSelectedSound(sound['id']!);
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم اختيار: ${sound['name']} 🎵'),
                        backgroundColor: AppColors.success,
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                ),
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }









  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header عصري مع تدرج
              Container(
                width: double.infinity,
                margin: const EdgeInsets.all(20),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // النص الترحيبي
                    Text(
                      'مرحباً بك في بالشفا! 👋',
                      style: TextStyle(
                        color: AppColors.textWhite,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'نظام إدارة الأدوية والمرضى',
                      style: TextStyle(
                        color: AppColors.textWhite.withOpacity(0.9),
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    // أزرار المنبه
                    const SizedBox(height: 30),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _testAlarm,
                        icon: const Icon(Icons.alarm),
                        label: const Text('اختبار المنبه'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppColors.primary,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _selectAlarmSound,
                        icon: const Icon(Icons.volume_up),
                        label: const Text('اختيار الصوت'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppColors.primary,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _rescheduleAlarms,
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة جدولة المنبهات'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _requestPermissions,
                        icon: const Icon(Icons.security),
                        label: const Text('طلب أذونات النظام'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.calendar_today,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.people,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${patients.length} مريض',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // قائمة المرضى
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  child: patients.isEmpty
                      ? _buildEmptyState()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'قائمة المرضى',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Expanded(
                              child: ListView.builder(
                                itemCount: patients.length,
                                itemBuilder: (context, index) {
                                  final patient = patients[index];
                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 12),
                                    decoration: BoxDecoration(
                                      color: AppColors.surface,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.shadow,
                                          blurRadius: 10,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: PatientTile(
                                      patient: patient,
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => PatientScreen(
                                              patient: patient,
                                            ),
                                          ),
                                        ).then((_) => _loadPatients());
                                      },
                                      onDelete: () async {
                                        await DatabaseService.instance
                                            .deletePatient(patient.id!);
                                        _loadPatients();
                                      },
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: Container(
        decoration: BoxDecoration(
          gradient: AppColors.primaryGradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.4),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: FloatingActionButton(
          onPressed: _addPatient,
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: const Icon(
            Icons.add,
            color: Colors.white,
            size: 28,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppColors.lightGreen,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.people_outline,
              size: 64,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد مرضى مضافين',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة مريض جديد',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}

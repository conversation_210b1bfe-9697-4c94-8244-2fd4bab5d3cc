# تعليمات اختبار نظام المنبه المحسن

## كيفية اختبار النظام

### 1. اختبار أساسي (التطبيق مفتوح)
1. ا<PERSON><PERSON><PERSON> التطبيق
2. أضف مريض جديد
3. أضف دواء للمريض
4. اجعل وقت الجرعة بعد دقيقة واحدة من الوقت الحالي
5. انتظر حتى يحين الوقت
6. **النتيجة المتوقعة**: ستظهر الشاشة الزرقاء تلقائياً مع الصوت والاهتزاز

### 2. اختبار التطبيق في الخلفية
1. اجعل وقت جرعة بعد دقيقتين
2. اضغط زر Home لوضع التطبيق في الخلفية
3. انتظر حتى يحين الوقت
4. **النتيجة المتوقعة**: ستظهر الشاشة الزرقاء فوق التطبيق الحالي

### 3. اختبار التطبيق مغلق
1. اجعل وقت جرعة بعد 3 دقائق
2. أغلق التطبيق تماماً (من قائمة التطبيقات الحديثة)
3. انتظر حتى يحين الوقت
4. **النتيجة المتوقعة**: ستظهر الشاشة الزرقاء حتى لو كان التطبيق مغلق

### 4. اختبار الهاتف في وضع السكون
1. اجعل وقت جرعة بعد دقيقتين
2. أغلق شاشة الهاتف (وضع السكون)
3. انتظر حتى يحين الوقت
4. **النتيجة المتوقعة**: ستضيء الشاشة وتظهر الشاشة الزرقاء

### 5. اختبار الإشعارات
1. عند ظهور إشعار الجرعة
2. اضغط على الإشعار
3. **النتيجة المتوقعة**: ستظهر الشاشة الزرقاء

### 6. اختبار الأزرار في الشاشة الزرقاء
1. عند ظهور الشاشة الزرقاء
2. جرب الضغط على "تم أخذ الجرعة ✅"
3. **النتيجة المتوقعة**: تختفي الشاشة ويتم تسجيل الجرعة
4. جرب الضغط على "ذكرني بعد 5 دقائق ⏰"
5. **النتيجة المتوقعة**: تختفي الشاشة وتظهر مرة أخرى بعد 5 دقائق

## نصائح للاختبار

### إعدادات الهاتف المطلوبة:
1. **تجاهل تحسين البطارية**: تأكد من إضافة التطبيق لقائمة الاستثناءات
2. **أذونات الإشعارات**: تأكد من تفعيل جميع أذونات الإشعارات
3. **عرض فوق التطبيقات**: تأكد من السماح للتطبيق بالعرض فوق التطبيقات الأخرى

### مراقبة النظام:
- راقب logs في وحدة التحكم لرؤية ما يحدث
- ابحث عن رسائل مثل "🚨 حان وقت الجرعة" و "✅ تم عرض الشاشة الزرقاء"

### في حالة عدم عمل النظام:
1. تحقق من الأذونات في إعدادات الهاتف
2. تأكد من أن التطبيق غير مدرج في قائمة تحسين البطارية
3. أعد تشغيل الهاتف وجرب مرة أخرى
4. تحقق من logs للأخطاء

## الملفات المحدثة للمراجعة:
- `lib/services/alarm_service.dart` - النظام الرئيسي للمنبه
- `lib/main.dart` - معالج الإشعارات
- `android/app/src/main/kotlin/com/example/balshifa/AlarmActivity.kt` - الشاشة الزرقاء
- `android/app/src/main/kotlin/com/example/balshifa/AlarmReceiver.kt` - مستقبل المنبهات
- `android/app/src/main/kotlin/com/example/balshifa/MainActivity.kt` - النشاط الرئيسي

النظام الآن يستخدم طرق متعددة لضمان عرض الشاشة الزرقاء، مما يجعله أكثر موثوقية!

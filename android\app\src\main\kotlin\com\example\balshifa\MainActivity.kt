package com.example.balshifa

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val ALARM_CHANNEL = "alarm_channel"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // قناة المنبه
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, ALARM_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "showFullScreenAlarm" -> {
                    val doseId = call.argument<String>("dose_id") ?: "0"
                    val medicineName = call.argument<String>("medicine_name") ?: "دواء"
                    val doseTime = call.argument<String>("dose_time") ?: "الآن"
                    val sound = call.argument<String>("sound") ?: "alarm_default"

                    val intent = Intent(this, AlarmActivity::class.java).apply {
                        putExtra("dose_id", doseId)
                        putExtra("medicine_name", medicineName)
                        putExtra("dose_time", doseTime)
                        putExtra("sound", sound)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    }

                    startActivity(intent)
                    result.success(true)
                }
                "scheduleAlarm" -> {
                    val id = call.argument<Int>("id") ?: 0
                    val payload = call.argument<String>("payload") ?: ""
                    val timeMillis = call.argument<Long>("timeMillis") ?: 0L

                    scheduleNativeAlarm(id, payload, timeMillis)
                    result.success(true)
                }
                "cancelAlarm" -> {
                    val id = call.argument<Int>("id") ?: 0
                    cancelNativeAlarm(id)
                    result.success(true)
                }
                "cancelAllAlarms" -> {
                    cancelAllAlarms()
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }



    override fun onDestroy() {
        super.onDestroy()
        alarmCheckReceiver?.let { unregisterReceiver(it) }
    }

    private fun scheduleNativeAlarm(id: Int, payload: String, timeMillis: Long) {
        try {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager

            val intent = Intent(this, AlarmReceiver::class.java).apply {
                putExtra("payload", payload)
            }

            val pendingIntent = PendingIntent.getBroadcast(
                this,
                id,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // جدولة المنبه
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                timeMillis,
                pendingIntent
            )

            android.util.Log.d("MainActivity", "تم جدولة منبه أصلي للجرعة $id في $timeMillis")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في جدولة المنبه: ${e.message}")
        }
    }
    
    private fun cancelNativeAlarm(id: Int) {
        try {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
            
            val intent = Intent(this, AlarmReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                this,
                id,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            alarmManager.cancel(pendingIntent)
            android.util.Log.d("MainActivity", "تم إلغاء المنبه $id")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في إلغاء المنبه: ${e.message}")
        }
    }
    
    private fun cancelAllAlarms() {
        try {
            android.util.Log.d("MainActivity", "🚫 بدء إلغاء جميع المنبهات...")
            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
            android.util.Log.d("MainActivity", "✅ تم إلغاء جميع المنبهات")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "❌ خطأ في إلغاء جميع المنبهات: ${e.message}")
        }
    }
}

package com.example.balshifa

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.RingtoneManager
import android.net.Uri
import android.os.Vibrator
import android.os.VibrationEffect
import android.os.Build
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val ALARM_CHANNEL = "alarm_channel"
    private var alarmCheckReceiver: BroadcastReceiver? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // تسجيل مستقبل فحص المنبهات
        setupAlarmCheckReceiver()

        // بدء الخدمة المقدمة
        AlarmForegroundService.startService(this)
        
        // فحص إذا كان التطبيق يفتح لإعادة جدولة المنبهات
        if (intent?.action == "RESCHEDULE_ALARMS") {
            android.util.Log.d("MainActivity", "إعادة جدولة المنبهات بعد إعادة التشغيل")
            // إرسال إشارة لـ Flutter لإعادة جدولة المنبهات
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, ALARM_CHANNEL)
                .invokeMethod("rescheduleAllAlarms", null)
        }

        // قناة المنبه
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, ALARM_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "showFullScreenAlarm" -> {
                    val doseId = call.argument<String>("dose_id") ?: "0"
                    val medicineName = call.argument<String>("medicine_name") ?: "دواء"
                    val doseTime = call.argument<String>("dose_time") ?: "الآن"
                    val sound = call.argument<String>("sound") ?: "alarm_default"

                    val intent = Intent(this, AlarmActivity::class.java).apply {
                        putExtra("dose_id", doseId)
                        putExtra("medicine_name", medicineName)
                        putExtra("dose_time", doseTime)
                        putExtra("sound", sound)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    }

                    startActivity(intent)
                    result.success(true)
                }
                "scheduleAlarm" -> {
                    val id = call.argument<Int>("id") ?: 0
                    val payload = call.argument<String>("payload") ?: ""
                    val timeMillis = call.argument<Long>("timeMillis") ?: 0L

                    scheduleNativeAlarm(id, payload, timeMillis)
                    result.success(true)
                }
                "cancelAlarm" -> {
                    val id = call.argument<Int>("id") ?: 0
                    cancelNativeAlarm(id)
                    result.success(true)
                }
                "snoozeAlarm" -> {
                    val id = call.argument<Int>("id") ?: 0
                    val payload = call.argument<String>("payload") ?: ""
                    val snoozeMinutes = call.argument<Int>("minutes") ?: 5
                    
                    snoozeNativeAlarm(id, payload, snoozeMinutes)
                    result.success(true)
                }
                "checkAlarms" -> {
                    // استدعاء Flutter لفحص المنبهات
                    MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, ALARM_CHANNEL)
                        .invokeMethod("checkAlarmsFromNative", null)
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun setupAlarmCheckReceiver() {
        alarmCheckReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    "com.example.balshifa.CHECK_ALARMS" -> {
                        // استدعاء Flutter لفحص المنبهات
                        flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                            MethodChannel(messenger, ALARM_CHANNEL)
                                .invokeMethod("checkAlarmsFromNative", null)
                        }
                    }
                    "com.example.balshifa.SHOW_ALARM" -> {
                        // عرض الشاشة الزرقاء مباشرة
                        val payload = intent.getStringExtra("payload") ?: ""
                        if (payload.isNotEmpty()) {
                            val parts = payload.split("|")
                            if (parts.size >= 3) {
                                val alarmIntent = Intent(this@MainActivity, AlarmActivity::class.java).apply {
                                    putExtra("dose_id", parts[0])
                                    putExtra("medicine_name", parts[1])
                                    putExtra("dose_time", parts[2])
                                    putExtra("sound", "alarm_default")
                                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                }
                                startActivity(alarmIntent)
                                android.util.Log.d("MainActivity", "تم عرض الشاشة الزرقاء من broadcast")
                            }
                        }
                    }
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction("com.example.balshifa.CHECK_ALARMS")
            addAction("com.example.balshifa.SHOW_ALARM")
        }
        registerReceiver(alarmCheckReceiver, filter)
    }

    override fun onDestroy() {
        super.onDestroy()
        alarmCheckReceiver?.let { unregisterReceiver(it) }
    }

    private fun scheduleNativeAlarm(id: Int, payload: String, timeMillis: Long) {
        try {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager

            val intent = Intent(this, AlarmReceiver::class.java).apply {
                putExtra("payload", payload)
            }

            val pendingIntent = PendingIntent.getBroadcast(
                this,
                id,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // جدولة المنبه
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                timeMillis,
                pendingIntent
            )

            android.util.Log.d("MainActivity", "تم جدولة منبه أصلي للجرعة $id في $timeMillis")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في جدولة المنبه: ${e.message}")
        }
    }
    
    private fun cancelNativeAlarm(id: Int) {
        try {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
            
            val intent = Intent(this, AlarmReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                this,
                id,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            alarmManager.cancel(pendingIntent)
            android.util.Log.d("MainActivity", "تم إلغاء المنبه $id")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في إلغاء المنبه: ${e.message}")
        }
    }
    
    private fun snoozeNativeAlarm(id: Int, payload: String, minutes: Int) {
        try {
            // إلغاء المنبه الحالي أولاً
            cancelNativeAlarm(id)
            
            // جدولة منبه جديد بعد المدة المحددة
            val snoozeTime = System.currentTimeMillis() + (minutes * 60 * 1000)
            
            // استخدام ID مختلف للتأجيل لتجنب التداخل مع المنبه الأصلي
            val snoozeId = id + 100000
            scheduleNativeAlarm(snoozeId, payload, snoozeTime)
            
            android.util.Log.d("MainActivity", "تم تأجيل المنبه $id لمدة $minutes دقائق")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في تأجيل المنبه: ${e.message}")
        }
    }
}

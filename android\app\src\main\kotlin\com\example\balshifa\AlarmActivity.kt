package com.example.balshifa

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.RingtoneManager
import android.net.Uri
import android.os.Bundle
import android.os.Vibrator
import android.os.VibrationEffect
import android.os.Build
import android.view.WindowManager
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.graphics.Color
import android.view.Gravity
import android.util.Log
import io.flutter.embedding.android.FlutterApplication
import io.flutter.plugin.common.MethodChannel

class AlarmActivity : Activity() {
    private var mediaPlayer: MediaPlayer? = null
    private var vibrator: Vibrator? = null
    private var doseId: String = "0"
    private var medicineName: String = ""
    private var doseTime: String = ""
    private var selectedSound: String = "alarm_default"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // إعداد النافذة لتظهر فوق جميع التطبيقات وتوقظ الشاشة
        window.addFlags(
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
            WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
            WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON
        )

        // جعل النشاط يظهر فوق شاشة القفل
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
        }

        // الحصول على البيانات من Intent
        doseId = intent.getStringExtra("dose_id") ?: "0"
        medicineName = intent.getStringExtra("medicine_name") ?: "دواء"
        doseTime = intent.getStringExtra("dose_time") ?: "الآن"
        selectedSound = intent.getStringExtra("sound") ?: "alarm_default"

        // إذا لم تكن البيانات متوفرة، حاول استخراجها من payload
        if (doseId == "0" && medicineName == "دواء") {
            val payload = intent.getStringExtra("payload") ?: ""
            if (payload.isNotEmpty()) {
                val parts = payload.split("|")
                if (parts.size >= 3) {
                    doseId = parts[0]
                    medicineName = parts[1]
                    doseTime = parts[2]
                }
            }
        }

        android.util.Log.d("AlarmActivity", "عرض منبه للدواء: $medicineName في الوقت: $doseTime")

        // إنشاء واجهة المنبه
        createAlarmLayout()

        // تشغيل الصوت والاهتزاز
        startAlarmSound()
        startVibration()
    }
    
    private fun createAlarmLayout() {
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setBackgroundColor(Color.parseColor("#1976D2")) // أزرق داكن
            setPadding(40, 40, 40, 40)
        }
        
        // أيقونة المنبه
        val alarmIcon = TextView(this).apply {
            text = "⏰"
            textSize = 80f
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 30)
        }
        
        // عنوان المنبه
        val titleText = TextView(this).apply {
            text = "وقت الجرعة!"
            textSize = 28f
            setTextColor(Color.WHITE)
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 20)
        }
        
        // اسم الدواء
        val medicineText = TextView(this).apply {
            text = "الدواء: $medicineName"
            textSize = 22f
            setTextColor(Color.WHITE)
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 10)
        }
        
        // وقت الجرعة
        val timeText = TextView(this).apply {
            text = "الوقت: $doseTime"
            textSize = 20f
            setTextColor(Color.WHITE)
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 40)
        }
        
        // زر "تم أخذ الجرعة"
        val takenButton = Button(this).apply {
            text = "تم أخذ الجرعة ✅"
            textSize = 18f
            setBackgroundColor(Color.parseColor("#4CAF50")) // أخضر
            setTextColor(Color.WHITE)
            setPadding(30, 20, 30, 20)
            setOnClickListener {
                markDoseAsTaken()
            }
        }
        
        // مساحة بين الأزرار
        val spacer = TextView(this).apply {
            text = ""
            setPadding(0, 20, 0, 0)
        }
        
        // زر "ذكرني بعد 5 دقائق"
        val snoozeButton = Button(this).apply {
            text = "ذكرني بعد 5 دقائق ⏰"
            textSize = 18f
            setBackgroundColor(Color.parseColor("#FF9800")) // برتقالي
            setTextColor(Color.WHITE)
            setPadding(30, 20, 30, 20)
            setOnClickListener {
                snoozeDose()
            }
        }
        
        // إضافة العناصر للـ Layout
        layout.addView(alarmIcon)
        layout.addView(titleText)
        layout.addView(medicineText)
        layout.addView(timeText)
        layout.addView(takenButton)
        layout.addView(spacer)
        layout.addView(snoozeButton)
        
        setContentView(layout)
    }
    
    private fun startAlarmSound() {
        try {
            stopAlarmSound() // إيقاف أي صوت سابق
            
            // اختيار الصوت حسب النوع المحدد
            val soundUri = when (selectedSound) {
                "alarm_classic" -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
                "alarm_gentle" -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                "alarm_loud" -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)
                "alarm_beep" -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
                else -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
            } ?: RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            
            mediaPlayer = MediaPlayer().apply {
                setDataSource(this@AlarmActivity, soundUri)
                setAudioStreamType(AudioManager.STREAM_ALARM)
                isLooping = true // تكرار مستمر
                prepare()
                start()
            }
            
            // رفع مستوى الصوت إلى الحد الأقصى
            val audioManager = getSystemService(AUDIO_SERVICE) as AudioManager
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_ALARM)
            audioManager.setStreamVolume(AudioManager.STREAM_ALARM, maxVolume, 0)
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private fun startVibration() {
        vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val vibrationEffect = VibrationEffect.createWaveform(
                longArrayOf(0, 1000, 500, 1000, 500, 1000), // نمط الاهتزاز
                0 // تكرار من البداية
            )
            vibrator?.vibrate(vibrationEffect)
        } else {
            @Suppress("DEPRECATION")
            vibrator?.vibrate(longArrayOf(0, 1000, 500, 1000, 500, 1000), 0)
        }
    }
    
    private fun stopAlarmSound() {
        mediaPlayer?.apply {
            if (isPlaying) {
                stop()
            }
            release()
        }
        mediaPlayer = null
    }
    
    private fun stopVibration() {
        vibrator?.cancel()
    }
    
    private fun markDoseAsTaken() {
        // إيقاف الصوت والاهتزاز
        stopAlarmSound()
        stopVibration()

        // إرسال إشعار إلى Flutter عبر MethodChannel
        try {
            val flutterEngine = (application as? FlutterApplication)?.let { app ->
                app.engines.firstOrNull()
            }

            flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                val channel = MethodChannel(messenger, "alarm_channel")
                channel.invokeMethod("doseTaken", mapOf("dose_id" to doseId))
            }
        } catch (e: Exception) {
            Log.e("AlarmActivity", "خطأ في إرسال إشعار أخذ الجرعة: ${e.message}")
        }

        // إغلاق النشاط
        finish()
    }

    private fun snoozeDose() {
        // إيقاف الصوت والاهتزاز
        stopAlarmSound()
        stopVibration()

        // إرسال إشعار إلى Flutter عبر MethodChannel
        try {
            val flutterEngine = (application as? FlutterApplication)?.let { app ->
                app.engines.firstOrNull()
            }

            flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                val channel = MethodChannel(messenger, "alarm_channel")
                channel.invokeMethod("doseSnooze", mapOf("dose_id" to doseId))
            }
        } catch (e: Exception) {
            Log.e("AlarmActivity", "خطأ في إرسال إشعار تأجيل الجرعة: ${e.message}")
        }

        // إغلاق النشاط
        finish()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopAlarmSound()
        stopVibration()
    }
    
    override fun onBackPressed() {
        // منع إغلاق المنبه بزر الرجوع
        // يجب الضغط على أحد الأزرار
    }
}

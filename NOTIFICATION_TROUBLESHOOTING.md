# دليل استكشاف أخطاء الإشعارات 🔧

## المشكلة الحالية
الإشعارات التجريبية تعمل بشكل صحيح، لكن الإشعارات التلقائية للجرعات المجدولة لا تظهر.

## الأسباب المحتملة

### 1. عدم وجود جرعات مجدولة
- **المشكلة**: لا توجد أدوية مضافة أو جرعات مجدولة في قاعدة البيانات
- **الحل**: إضافة مريض ودواء جديد مع جرعات مجدولة

### 2. مشكلة في توقيت الجرعات
- **المشكلة**: الجرعات المجدولة في أوقات بعيدة أو في الماضي
- **الحل**: التأكد من أن الجرعات مجدولة في أوقات قريبة

### 3. مشكلة في أذونات الإشعارات
- **المشكلة**: عدم منح التطبيق أذونات الإشعارات
- **الحل**: التحقق من إعدادات الجهاز ومنح الأذونات

### 4. مشكلة في نظام التحقق من الجرعات
- **المشكلة**: DoseChecker لا يعمل بشكل صحيح
- **الحل**: إعادة تشغيل النظام

## الأدوات المضافة للتشخيص

### في الشاشة الرئيسية:
1. **🔔 زر اختبار الإشعارات**: لاختبار النظام الأساسي
2. **🐛 زر تشخيص الإشعارات**: لفحص حالة قاعدة البيانات
3. **⏰ زر إنشاء جرعة تجريبية**: لإنشاء جرعة تصل خلال دقيقة
4. **🔄 زر إعادة تشغيل النظام**: لإعادة تشغيل نظام التحقق
5. **🔒 زر التحقق من الأذونات**: للتأكد من الأذونات

## خطوات التشخيص

### الخطوة 1: التحقق من قاعدة البيانات
1. اضغط على زر "تشخيص الإشعارات" 🐛
2. تحقق من النتائج:
   - عدد المرضى
   - عدد الأدوية
   - عدد الجرعات المجدولة
   - الجرعات في الساعة القادمة

### الخطوة 2: إنشاء جرعة تجريبية
1. اضغط على زر "إنشاء جرعة تجريبية" ⏰
2. انتظر دقيقة واحدة
3. يجب أن تصل الإشعار تلقائياً

### الخطوة 3: التحقق من الأذونات
1. اضغط على زر "التحقق من الأذونات" 🔒
2. تحقق من رسائل وحدة التحكم
3. تأكد من منح الأذونات في إعدادات الجهاز

### الخطوة 4: إعادة تشغيل النظام
1. اضغط على زر "إعادة تشغيل النظام" 🔄
2. سيتم إعادة تشغيل نظام التحقق من الجرعات

## التحسينات المضافة

### 1. تحسين دقة التوقيت
- تم تقليل فترة التحقق من 5 دقائق إلى دقيقة واحدة
- تم تقليل نافذة التحقق من ±5 دقائق إلى ±2 دقيقة

### 2. إضافة معلومات التشخيص
- رسائل مفصلة في وحدة التحكم
- عداد للجرعات المفحوصة والإشعارات المرسلة

### 3. تحسين طلب الأذونات
- طلب أذونات الإشعارات تلقائياً عند التهيئة
- رسائل واضحة عن حالة الأذونات

## نصائح للاستخدام

### للاختبار السريع:
1. أضف مريض جديد
2. أضف دواء بجرعة كل ساعة
3. اضبط الوقت الأول للجرعة بعد دقيقتين من الآن
4. انتظر وصول الإشعار

### للاستخدام العادي:
1. تأكد من إضافة الأدوية بأوقات صحيحة
2. تحقق من أذونات الإشعارات في إعدادات الجهاز
3. لا تغلق التطبيق بالكامل (اتركه في الخلفية)

## إعدادات الجهاز المطلوبة

### أندرويد:
1. **الإشعارات**: مفعلة للتطبيق
2. **التشغيل في الخلفية**: مسموح
3. **توفير البطارية**: استثناء التطبيق من توفير البطارية
4. **التشغيل التلقائي**: مفعل للتطبيق

## رسائل وحدة التحكم

تحقق من وحدة التحكم للرسائل التالية:
- `🚀 بدء نظام التحقق من الجرعات...`
- `🔍 بدء التحقق من الجرعات...`
- `🔔 وقت الجرعة! الفرق: X دقيقة`
- `📢 إرسال إشعار للدواء: اسم الدواء`
- `✅ انتهى التحقق - فحص X جرعة، أرسل Y إشعار`

## الدعم
إذا استمرت المشكلة بعد تطبيق هذه الخطوات، تحقق من:
1. إصدار أندرويد (يجب أن يكون 5.0 أو أحدث)
2. مساحة التخزين المتاحة
3. إعادة تثبيت التطبيق

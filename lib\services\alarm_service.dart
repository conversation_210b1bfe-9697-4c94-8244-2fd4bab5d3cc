import 'dart:async';
import 'package:flutter/services.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/utils/time_formatter.dart';

class AlarmService {
  static final AlarmService _instance = AlarmService._internal();
  factory AlarmService() => _instance;
  AlarmService._internal();

  static const platform = MethodChannel('alarm_channel');

  String _selectedSound = 'alarm_default';

  // تهيئة النظام
  Future<void> initialize() async {
    print('🚨 تهيئة نظام المنبه الجديد (AlarmManager)...');

    // إعداد معالج للاستدعاءات من الخدمة الأصلية
    _setupNativeCallHandler();

    print('✅ تم تهيئة نظام المنبه الجديد بنجاح');
  }

  // إعداد معالج للاستدعاءات من الخدمة الأصلية
  void _setupNativeCallHandler() {
    platform.setMethodCallHandler((call) async {
      if (call.method == 'alarmTriggered') {
        final payload = call.arguments['payload'] as String?;
        if (payload != null) {
          print('🚨 تم تشغيل منبه من النظام الأصلي: $payload');
          await _showFullScreenAlarm(payload);
        }
      } else if (call.method == 'doseTaken') {
        final doseId = call.arguments['dose_id'] as String?;
        if (doseId != null) {
          await _markDoseAsTaken(doseId);
        }
      } else if (call.method == 'doseSnooze') {
        final doseId = call.arguments['dose_id'] as String?;
        if (doseId != null) {
          await _snoozeDose(doseId, 5);
        }
      }
    });
  }

  // إظهار منبه ملء الشاشة
  Future<void> _showFullScreenAlarm(String? payload) async {
    try {
      if (payload == null) return;

      // استخراج معلومات الجرعة
      final parts = payload.split('|');
      if (parts.length < 3) return;

      final doseId = parts[0];
      final medicineName = parts[1];
      final doseTime = parts[2];

      // إظهار النافذة المنبثقة مع الصوت
      await platform.invokeMethod('showFullScreenAlarm', {
        'dose_id': doseId,
        'medicine_name': medicineName,
        'dose_time': doseTime,
        'sound': _selectedSound,
      });

      print('✅ تم عرض الشاشة الزرقاء للجرعة: $medicineName');

    } catch (e) {
      print('❌ خطأ في إظهار المنبه: $e');
    }
  }

  // تأجيل الجرعة لمدة 5 دقائق
  Future<void> _snoozeDose(String doseId, int minutes) async {
    try {
      final id = int.parse(doseId);
      final snoozeTime = DateTime.now().add(Duration(minutes: minutes));

      // الحصول على معلومات الجرعة الأصلية
      final db = DatabaseService.instance;
      final dose = await db.getDoseById(id);
      if (dose == null) return;

      final medicine = await db.getMedicineById(dose.medicineId);
      if (medicine == null) return;

      final timeString = TimeFormatter.formatTime12Hour(snoozeTime);
      final payload = '$doseId|${medicine.name}|$timeString';

      // جدولة منبه جديد للتأجيل (معرف مختلف)
      await platform.invokeMethod('scheduleAlarm', {
        'id': id + 100000, // معرف مختلف للتأجيل
        'payload': payload,
        'timeMillis': snoozeTime.millisecondsSinceEpoch,
      });

      print('⏰ تم تأجيل الجرعة $doseId لمدة $minutes دقائق');
    } catch (e) {
      print('❌ خطأ في تأجيل الجرعة: $e');
    }
  }

  // تسجيل الجرعة كمأخوذة
  Future<void> _markDoseAsTaken(String doseId) async {
    try {
      final id = int.parse(doseId);

      // تحديث قاعدة البيانات
      final db = DatabaseService.instance;
      final dose = await db.getDoseById(id);

      if (dose != null) {
        final takenDose = Dose(
          id: dose.id,
          medicineId: dose.medicineId,
          scheduledDateTime: dose.scheduledDateTime,
          takenDateTime: DateTime.now(),
          status: 'taken',
        );

        await db.updateDose(takenDose);

        // إلغاء أي منبهات مؤجلة لهذه الجرعة
        await _cancelAlarm(id);
        await _cancelAlarm(id + 100000); // إلغاء التأجيل أيضاً

        print('✅ تم تسجيل الجرعة $doseId كمأخوذة');
      }
    } catch (e) {
      print('❌ خطأ في تسجيل الجرعة: $e');
    }
  }

  // جدولة منبه لجرعة (دالة داخلية)
  Future<void> _scheduleAlarm(int doseId, String medicineName, DateTime alarmTime) async {
    try {
      final timeString = TimeFormatter.formatTime12Hour(alarmTime);
      final payload = '$doseId|$medicineName|$timeString';

      print('🚨 جدولة منبه AlarmManager للدواء: $medicineName في $alarmTime');

      // جدولة منبه باستخدام AlarmManager
      await platform.invokeMethod('scheduleAlarm', {
        'id': doseId,
        'payload': payload,
        'timeMillis': alarmTime.millisecondsSinceEpoch,
      });

      print('✅ تم جدولة منبه AlarmManager للدواء: $medicineName في $timeString');
    } catch (e) {
      print('❌ خطأ في جدولة المنبه: $e');
    }
  }

  // جدولة جميع منبهات الجرعات
  Future<void> scheduleAllDoseAlarms() async {
    try {
      print('🚨 بدء جدولة جميع منبهات الجرعات باستخدام AlarmManager...');

      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();
      print('👥 عدد المرضى: ${patients.length}');

      int totalScheduled = 0;
      int totalDoses = 0;
      final now = DateTime.now();
      print('⏰ الوقت الحالي: ${now.toString()}');

      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);
        print('💊 المريض ${patient.name}: ${medicines.length} أدوية');

        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);
          print('📋 الدواء ${medicine.name}: ${scheduledDoses.length} جرعة مجدولة');

          for (final dose in scheduledDoses) {
            totalDoses++;

            // جدولة المنبه فقط للجرعات المستقبلية
            if (dose.scheduledDateTime.isAfter(now)) {
              await _scheduleAlarm(dose.id!, medicine.name, dose.scheduledDateTime);
              totalScheduled++;
              print('✅ تم جدولة منبه للجرعة ${dose.id} في ${dose.scheduledDateTime}');
            } else {
              print('⏭️ تم تخطي الجرعة ${dose.id} (في الماضي): ${dose.scheduledDateTime}');
            }
          }
        }
      }

      print('🎯 انتهت جدولة المنبهات: $totalScheduled من أصل $totalDoses جرعة');
    } catch (e) {
      print('❌ خطأ في جدولة منبهات الجرعات: $e');
    }
  }

  // جدولة منبه لجرعة محددة (دالة عامة)
  Future<void> scheduleAlarmForDose(int doseId, String medicineName, DateTime alarmTime) async {
    await _scheduleAlarm(doseId, medicineName, alarmTime);
  }

  // إلغاء منبه
  Future<void> _cancelAlarm(int doseId) async {
    try {
      await platform.invokeMethod('cancelAlarm', {'id': doseId});
      print('🚫 تم إلغاء المنبه للجرعة $doseId');
    } catch (e) {
      print('❌ خطأ في إلغاء المنبه: $e');
    }
  }

  // إلغاء جميع المنبهات
  Future<void> cancelAllAlarms() async {
    try {
      await platform.invokeMethod('cancelAllAlarms');
      print('🚫 تم إلغاء جميع المنبهات');
    } catch (e) {
      print('❌ خطأ في إلغاء جميع المنبهات: $e');
    }
  }
}
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/utils/time_formatter.dart';
import 'package:flutter/services.dart';

class AlarmService {
  static final AlarmService _instance = AlarmService._internal();
  factory AlarmService() => _instance;
  AlarmService._internal();

  // قناة التواصل مع الكود الأصلي
  static const platform = MethodChannel('alarm_channel');

  // إعدادات الصوت
  String _selectedSound = 'alarm_default';

  // نظام مراقبة إضافي
  Timer? _backgroundChecker;

  // قائمة الأصوات المتاحة
  static const List<Map<String, String>> availableSounds = [
    {'id': 'alarm_default', 'name': 'منبه افتراضي'},
    {'id': 'alarm_classic', 'name': 'منبه كلاسيكي'},
    {'id': 'alarm_gentle', 'name': 'منبه هادئ'},
    {'id': 'alarm_loud', 'name': 'منبه قوي'},
    {'id': 'alarm_beep', 'name': 'صفارة'},
  ];

  Future<void> initialize() async {
    print('🚨 تهيئة نظام المنبه الجديد (AlarmManager)...');
    
    // إعداد معالج للاستدعاءات من الخدمة الأصلية
    _setupNativeCallHandler();

    // بدء نظام المراقبة الإضافي
    _startBackgroundChecker();

    // فحص فوري للجرعات المستحقة عند بدء التطبيق
    await _checkForDueAlarms();

    print('✅ تم تهيئة نظام المنبه الجديد بنجاح');
  }

  // بدء نظام مراقبة إضافي للتأكد من عرض الشاشة
  void _startBackgroundChecker() {
    print('🔄 بدء نظام المراقبة الإضافي (كل 30 ثانية)...');

    _backgroundChecker = Timer.periodic(const Duration(seconds: 30), (timer) async {
      await _checkForDueAlarms();
    });
  }

  // فحص الجرعات المستحقة وعرض الشاشة إذا لزم الأمر
  Future<void> _checkForDueAlarms() async {
    try {
      final now = DateTime.now();
      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();

      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);

        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);

          for (final dose in scheduledDoses) {
            // إذا كانت الجرعة في الوقت المحدد أو متأخرة بـ 0-1 دقيقة، اعرض الشاشة
            final difference = now.difference(dose.scheduledDateTime).inSeconds;
            if (difference >= -30 && difference <= 60 && dose.status == 'scheduled') {
              print('🚨 حان وقت الجرعة: ${medicine.name} - عرض الشاشة الآن!');
              final timeString = TimeFormatter.formatTime12Hour(dose.scheduledDateTime);
              final payload = '${dose.id}|${medicine.name}|$timeString';

              // عرض الشاشة الزرقاء مباشرة
              await _showFullScreenAlarm(payload);

              return; // عرض شاشة واحدة فقط في كل مرة
            }
          }
        }
      }
    } catch (e) {
      print('❌ خطأ في فحص الجرعات المستحقة: $e');
    }
  }

  // إعداد معالج للاستدعاءات من الخدمة الأصلية
  void _setupNativeCallHandler() {
    platform.setMethodCallHandler((call) async {
      if (call.method == 'checkAlarmsFromNative') {
        print('📱 تم استلام طلب فحص المنبهات من الخدمة الأصلية');
        await _checkForDueAlarms();
      } else if (call.method == 'alarmTriggered') {
        final payload = call.arguments['payload'] as String?;
        if (payload != null) {
          print('🚨 تم تشغيل منبه من النظام الأصلي: $payload');
          await _showFullScreenAlarm(payload);
        }
      } else if (call.method == 'doseTaken') {
        final doseId = call.arguments['dose_id'] as String?;
        if (doseId != null) {
          await _markDoseAsTaken(doseId);
        }
      } else if (call.method == 'doseSnooze') {
        final doseId = call.arguments['dose_id'] as String?;
        if (doseId != null) {
          await _snoozeDose(doseId, 5); // تأجيل 5 دقائق
        }
      } else if (call.method == 'rescheduleAllAlarms') {
        print('📱 إعادة جدولة جميع المنبهات بعد إعادة التشغيل');
        await scheduleAllDoseAlarms();
      }
    });
  }

  // إظهار منبه ملء الشاشة
  Future<void> _showFullScreenAlarm(String? payload) async {
    try {
      if (payload == null) return;
      
      // استخراج معلومات الجرعة
      final parts = payload.split('|');
      if (parts.length < 3) return;
      
      final doseId = parts[0];
      final medicineName = parts[1];
      final doseTime = parts[2];
      
      // إظهار النافذة المنبثقة مع الصوت
      await platform.invokeMethod('showFullScreenAlarm', {
        'dose_id': doseId,
        'medicine_name': medicineName,
        'dose_time': doseTime,
        'sound': _selectedSound,
      });
      
      print('✅ تم عرض الشاشة الزرقاء للجرعة: $medicineName');
      
    } catch (e) {
      print('❌ خطأ في إظهار المنبه: $e');
    }
  }

  // تأجيل الجرعة بعدد دقائق محدد
  Future<void> _snoozeDose(String doseId, int minutes) async {
    try {
      final id = int.parse(doseId);
      
      // الحصول على معلومات الجرعة
      final db = DatabaseService.instance;
      final dose = await db.getDoseById(id);
      if (dose == null) return;
      
      final medicine = await db.getMedicineById(dose.medicineId);
      if (medicine == null) return;
      
      // إنشاء payload للتأجيل
      final snoozeTime = DateTime.now().add(Duration(minutes: minutes));
      final timeString = TimeFormatter.formatTime12Hour(snoozeTime);
      final payload = '$doseId|${medicine.name}|$timeString';
      
      // جدولة منبه جديد للتأجيل
      await platform.invokeMethod('snoozeAlarm', {
        'id': id,
        'payload': payload,
        'minutes': minutes,
      });
      
      print('⏰ تم تأجيل الجرعة $doseId لمدة $minutes دقائق');
    } catch (e) {
      print('❌ خطأ في تأجيل الجرعة: $e');
    }
  }

  // تسجيل الجرعة كمأخوذة
  Future<void> _markDoseAsTaken(String doseId) async {
    try {
      final id = int.parse(doseId);
      
      // تحديث قاعدة البيانات
      final db = DatabaseService.instance;
      final dose = await db.getDoseById(id);
      
      if (dose != null) {
        final takenDose = Dose(
          id: dose.id,
          medicineId: dose.medicineId,
          scheduledDateTime: dose.scheduledDateTime,
          takenDateTime: DateTime.now(),
          status: 'taken',
        );
        
        await db.updateDose(takenDose);
        
        // إلغاء أي منبهات مؤجلة لهذه الجرعة
        await _cancelAlarm(id);
        
        print('✅ تم تسجيل الجرعة $doseId كمأخوذة');
      }
    } catch (e) {
      print('❌ خطأ في تسجيل الجرعة: $e');
    }
  }

  // جدولة منبه لجرعة واحدة (دالة عامة)
  Future<void> scheduleAlarmForDose(int doseId, String medicineName, DateTime alarmTime) async {
    await _scheduleAlarm(doseId, medicineName, alarmTime);
  }

  // جدولة منبه لجرعة (دالة داخلية)
  Future<void> _scheduleAlarm(int doseId, String medicineName, DateTime alarmTime) async {
    try {
      final timeString = TimeFormatter.formatTime12Hour(alarmTime);
      final payload = '$doseId|$medicineName|$timeString';

      print('🚨 جدولة منبه AlarmManager للدواء: $medicineName في $alarmTime');

      // جدولة منبه باستخدام AlarmManager
      await platform.invokeMethod('scheduleAlarm', {
        'id': doseId,
        'payload': payload,
        'timeMillis': alarmTime.millisecondsSinceEpoch,
      });

      print('✅ تم جدولة منبه AlarmManager للدواء: $medicineName في $timeString');
    } catch (e) {
      print('❌ خطأ في جدولة المنبه: $e');
    }
  }

  // جدولة جميع منبهات الجرعات
  Future<void> scheduleAllDoseAlarms() async {
    try {
      print('🚨 بدء جدولة جميع منبهات الجرعات باستخدام AlarmManager...');

      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();
      print('👥 عدد المرضى: ${patients.length}');

      int totalScheduled = 0;
      int totalDoses = 0;
      final now = DateTime.now();
      print('⏰ الوقت الحالي: ${now.toString()}');

      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);
        print('💊 المريض ${patient.name}: ${medicines.length} أدوية');

        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);
          totalDoses += scheduledDoses.length;
          print('📋 الدواء ${medicine.name}: ${scheduledDoses.length} جرعات مجدولة');

          for (final dose in scheduledDoses) {
            print('⏱️ جرعة في: ${dose.scheduledDateTime.toString()} (ID: ${dose.id})');

            // جدولة المنبهات للجرعات المستقبلية فقط
            if (dose.scheduledDateTime.isAfter(now) && dose.id != null) {
              print('✅ جدولة منبه للجرعة ${dose.id}');
              await _scheduleAlarm(dose.id!, medicine.name, dose.scheduledDateTime);
              totalScheduled++;
            } else {
              print('❌ تخطي الجرعة ${dose.id} - وقت ماضي أو ID فارغ');
            }
          }
        }
      }

      print('📊 إجمالي الجرعات: $totalDoses');
      print('✅ تم جدولة $totalScheduled منبه للجرعات المستقبلية');
    } catch (e) {
      print('❌ خطأ في جدولة المنبهات: $e');
    }
  }

  // تغيير الصوت المحدد
  void setSelectedSound(String soundId) {
    _selectedSound = soundId;
    print('🔊 تم تغيير الصوت إلى: $soundId');
  }

  // دالة عامة لعرض الشاشة مباشرة (يمكن استدعاؤها من أي مكان)
  static Future<void> showAlarmScreen(String? payload) async {
    final alarmService = AlarmService();
    await alarmService._showFullScreenAlarm(payload);
  }

  // الحصول على الصوت المحدد
  String get selectedSound => _selectedSound;

  // إلغاء منبه معين
  Future<void> _cancelAlarm(int doseId) async {
    try {
      await platform.invokeMethod('cancelAlarm', {'id': doseId});
      print('❌ تم إلغاء المنبه: $doseId');
    } catch (e) {
      print('❌ خطأ في إلغاء المنبه: $e');
    }
  }

  // إلغاء منبه معين (دالة عامة)
  Future<void> cancelAlarm(int doseId) async {
    await _cancelAlarm(doseId);
  }

  // إلغاء جميع المنبهات
  Future<void> cancelAllAlarms() async {
    try {
      // سيتم تنفيذ هذا من خلال إعادة جدولة الكل
      await scheduleAllDoseAlarms();
      print('❌ تم إلغاء وإعادة جدولة جميع المنبهات');
    } catch (e) {
      print('❌ خطأ في إلغاء المنبهات: $e');
    }
  }

  // تنظيف الموارد
  void dispose() {
    _backgroundChecker?.cancel();
  }
}

# 🔐 معلومات الأمان - تطبيق بالشفا

## 📋 معلومات التوقيع

تم إنشاء ملف التوقيع بنجاح للتطبيق:

### 📁 الملفات المنشأة:
- **Keystore:** `android/app/upload-keystore.jks`
- **إعدادات:** `android/key.properties`

### 🔑 معلومات المفتاح:
- **Key Alias:** upload
- **Algorithm:** RSA 2048-bit
- **Validity:** 10,000 days (~27 years)
- **Certificate:** Self-signed

### 🏢 معلومات الشهادة:
- **CN:** Balshifa Medical App
- **OU:** Development Team
- **O:** Balshifa Medical
- **L:** Cairo
- **ST:** Cairo
- **C:** EG

## ⚠️ تحذيرات أمنية مهمة

### 🚨 احتفظ بهذه الملفات آمنة:
1. **upload-keystore.jks** - لا تفقد هذا الملف أبداً!
2. **key.properties** - لا تشارك كلمات المرور
3. **كلمة المرور:** `Balshifa2024!`

### 📝 نصائح الأمان:
- ✅ احتفظ بنسخة احتياطية من keystore في مكان آمن
- ✅ لا تضع key.properties في Git
- ✅ استخدم كلمات مرور قوية
- ✅ احتفظ بسجل لمعلومات الشهادة
- ❌ لا تشارك هذه الملفات مع أحد
- ❌ لا ترفع keystore على الإنترنت

## 🔄 استخدام التوقيع

### للبناء مع التوقيع:
```bash
# بناء APK موقع
flutter build apk --release

# بناء App Bundle موقع
flutter build appbundle --release
```

### التحقق من التوقيع:
```bash
# فحص APK
jarsigner -verify -verbose -certs app-release.apk

# معلومات الشهادة
keytool -list -v -keystore upload-keystore.jks -alias upload
```

## 🆘 في حالة فقدان الملفات

### إذا فقدت keystore:
1. **للتطبيقات الجديدة:** أنشئ keystore جديد
2. **للتطبيقات المنشورة:** اتصل بـ Google Play Support
3. **النسخ الاحتياطية:** تحقق من النسخ الاحتياطية

### إذا نسيت كلمة المرور:
- لا يمكن استرداد كلمة المرور
- ستحتاج لإنشاء keystore جديد
- للتطبيقات المنشورة: مشكلة كبيرة!

## 📊 معلومات إضافية

### حجم الملفات:
- **Keystore:** ~2KB
- **Certificate:** RSA-2048
- **Signature:** SHA256withRSA

### صلاحية الشهادة:
- **تاريخ الإنشاء:** اليوم
- **تاريخ الانتهاء:** بعد 27 سنة
- **متبقي:** 10,000 يوم

## 🔧 استكشاف الأخطاء

### مشاكل التوقيع الشائعة:
```bash
# خطأ في المسار
storeFile=app/upload-keystore.jks

# خطأ في كلمة المرور
storePassword=كلمة_المرور_الصحيحة

# خطأ في الـ alias
keyAlias=upload
```

### فحص الإعدادات:
```bash
# التحقق من وجود الملفات
ls android/app/upload-keystore.jks
ls android/key.properties

# فحص محتوى key.properties
cat android/key.properties
```

## 📱 للنشر على Google Play

### متطلبات Google Play:
- ✅ استخدم App Bundle (.aab)
- ✅ وقع بـ upload key
- ✅ Google Play App Signing
- ✅ فعل Play App Signing

### خطوات النشر:
1. بناء App Bundle موقع
2. رفع على Play Console
3. Google يوقع بـ app signing key
4. توزيع للمستخدمين

## 🎯 الخلاصة

تم إعداد التوقيع بنجاح! التطبيق جاهز للبناء والنشر بأمان.

**تذكر:** احتفظ بملفات التوقيع في مكان آمن ولا تشاركها مع أحد!

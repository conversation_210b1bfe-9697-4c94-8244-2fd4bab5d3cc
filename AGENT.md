# Balshifa Flutter App - Agent Guide

## Build & Test Commands
- **Run app**: `flutter run`
- **Build**: `flutter build apk` or `flutter build appbundle`
- **Test**: `flutter test`
- **Test single file**: `flutter test test/widget_test.dart`
- **Analyze**: `flutter analyze`
- **Format**: `dart format .`
- **Clean**: `flutter clean && flutter pub get`

## Architecture
- **Flutter app** for medical appointment/medication management (Arabic)
- **Database**: SQLite via sqflite package
- **Notifications**: flutter_local_notifications with background handlers
- **Platform channels**: Native Android/iOS alarm integration
- **Core services**: AlarmService, DatabaseService for scheduling and persistence

## Project Structure
- `lib/models/` - Data models (Patient, Medicine, Dose)
- `lib/services/` - Business logic and database operations
- `lib/screens/` - UI screens and pages
- `lib/widgets/` - Reusable UI components
- `lib/utils/` - Helper functions and utilities
- `lib/constants/` - App colors, styles, and constants

## Code Style
- **Imports**: Use `package:balshifa/` prefix for internal modules
- **Naming**: snake_case for files, camelCase for variables, PascalCase for classes
- **Linting**: Uses flutter_lints package (see analysis_options.yaml)
- **Comments**: Arabic comments for business logic, English for technical code
- **Error handling**: Try-catch blocks with descriptive error messages in Arabic

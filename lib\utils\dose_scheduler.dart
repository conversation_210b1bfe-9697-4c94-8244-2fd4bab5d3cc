import 'package:balshifa/models/dose.dart';
import 'package:balshifa/models/medicine.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/services/alarm_service.dart';

class DoseScheduler {
  static Future<void> scheduleDoses(Medicine medicine) async {
    final intervalHours = 24 ~/ medicine.timesPerDay;
    final doses = <Dose>[];
    
    DateTime currentDateTime = medicine.firstDoseDateTime;
    
    // حساب عدد الجرعات (إذا كانت مدة العلاج محددة)
    int totalDoses = medicine.durationDays != null
        ? medicine.durationDays! * medicine.timesPerDay
        : 30 * medicine.timesPerDay; // افتراضي 30 يوم
    
    for (int i = 0; i < totalDoses; i++) {
      doses.add(Dose(
        medicineId: medicine.id!,
        scheduledDateTime: currentDateTime,
        status: 'scheduled',
      ));
      
      currentDateTime = currentDateTime.add(Duration(hours: intervalHours));
    }
    
    // حفظ الجرعات في قاعدة البيانات
    for (final dose in doses) {
      await DatabaseService.instance.insertDose(dose);
    }

    // إعادة جدولة جميع المنبهات
    await AlarmService().scheduleAllDoseAlarms();
  }
}

{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\balshifa\\android\\app\\.cxx\\RelWithDebInfo\\5h736a1e\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\balshifa\\android\\app\\.cxx\\RelWithDebInfo\\5h736a1e\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
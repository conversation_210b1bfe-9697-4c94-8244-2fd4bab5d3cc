package com.example.balshifa

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.*

class AlarmForegroundService : Service() {
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var isRunning = false
    
    companion object {
        const val CHANNEL_ID = "alarm_foreground_channel"
        const val NOTIFICATION_ID = 1001
        
        fun startService(context: Context) {
            val intent = Intent(context, AlarmForegroundService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, AlarmForegroundService::class.java)
            context.stopService(intent)
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        Log.d("AlarmForegroundService", "تم إنشاء الخدمة المقدمة")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startForeground(NOTIFICATION_ID, createNotification())
        
        if (!isRunning) {
            isRunning = true
            startAlarmChecker()
            Log.d("AlarmForegroundService", "تم بدء فحص المنبهات")
        }
        
        return START_STICKY // إعادة تشغيل الخدمة إذا تم إيقافها
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        isRunning = false
        serviceScope.cancel()
        Log.d("AlarmForegroundService", "تم إيقاف الخدمة المقدمة")
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "خدمة منبه الأدوية",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "خدمة تعمل في الخلفية لمراقبة مواعيد الأدوية"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("بالشفا - مراقبة الأدوية")
            .setContentText("يتم مراقبة مواعيد الأدوية في الخلفية")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setOngoing(true)
            .setAutoCancel(false)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    private fun startAlarmChecker() {
        serviceScope.launch {
            while (isRunning) {
                try {
                    // فحص المنبهات كل 30 ثانية
                    checkAlarms()
                    delay(30000) // 30 ثانية
                } catch (e: Exception) {
                    Log.e("AlarmForegroundService", "خطأ في فحص المنبهات: ${e.message}")
                    delay(60000) // انتظار دقيقة في حالة الخطأ
                }
            }
        }
    }
    
    private suspend fun checkAlarms() {
        // هنا سنستدعي Flutter لفحص المنبهات
        withContext(Dispatchers.Main) {
            try {
                // إرسال broadcast للتطبيق لفحص المنبهات
                val intent = Intent("com.example.balshifa.CHECK_ALARMS")
                sendBroadcast(intent)

                Log.d("AlarmForegroundService", "تم إرسال طلب فحص المنبهات")

                // إضافة فحص إضافي للتأكد من عمل النظام
                Log.d("AlarmForegroundService", "الخدمة تعمل - الوقت الحالي: ${System.currentTimeMillis()}")
            } catch (e: Exception) {
                Log.e("AlarmForegroundService", "خطأ في إرسال broadcast: ${e.message}")
            }
        }
    }
}

import 'package:flutter/material.dart';

class TimeFormatter {
  /// تحويل الوقت من 24 ساعة إلى 12 ساعة مع AM/PM
  static String formatTime12Hour(DateTime dateTime) {
    int hour = dateTime.hour;
    int minute = dateTime.minute;
    String period = hour >= 12 ? 'مساءً' : 'صباحاً';
    
    // تحويل إلى نظام 12 ساعة
    if (hour == 0) {
      hour = 12; // منتصف الليل
    } else if (hour > 12) {
      hour = hour - 12;
    }
    
    String minuteStr = minute.toString().padLeft(2, '0');
    return '$hour:$minuteStr $period';
  }
  
  /// تحويل الوقت من 24 ساعة إلى 12 ساعة مع AM/PM (إنجليزي)
  static String formatTime12HourEnglish(DateTime dateTime) {
    int hour = dateTime.hour;
    int minute = dateTime.minute;
    String period = hour >= 12 ? 'PM' : 'AM';
    
    // تحويل إلى نظام 12 ساعة
    if (hour == 0) {
      hour = 12; // منتصف الليل
    } else if (hour > 12) {
      hour = hour - 12;
    }
    
    String minuteStr = minute.toString().padLeft(2, '0');
    return '$hour:$minuteStr $period';
  }
  
  /// تحويل TimeOfDay إلى نص بنظام 12 ساعة
  static String formatTimeOfDay12Hour(TimeOfDay timeOfDay) {
    int hour = timeOfDay.hour;
    int minute = timeOfDay.minute;
    String period = hour >= 12 ? 'مساءً' : 'صباحاً';
    
    // تحويل إلى نظام 12 ساعة
    if (hour == 0) {
      hour = 12; // منتصف الليل
    } else if (hour > 12) {
      hour = hour - 12;
    }
    
    String minuteStr = minute.toString().padLeft(2, '0');
    return '$hour:$minuteStr $period';
  }
  
  /// تحويل من نص 12 ساعة إلى TimeOfDay
  static TimeOfDay parseTime12Hour(String timeString) {
    // مثال: "2:30 مساءً" أو "10:15 صباحاً"
    try {
      final parts = timeString.split(' ');
      final timePart = parts[0];
      final period = parts[1];
      
      final timeParts = timePart.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);
      
      // تحويل إلى نظام 24 ساعة
      if (period == 'مساءً' || period == 'PM') {
        if (hour != 12) {
          hour += 12;
        }
      } else if (period == 'صباحاً' || period == 'AM') {
        if (hour == 12) {
          hour = 0;
        }
      }
      
      return TimeOfDay(hour: hour, minute: minute);
    } catch (e) {
      // في حالة الخطأ، إرجاع الوقت الحالي
      return TimeOfDay.now();
    }
  }
  
  /// تحويل من نص 12 ساعة إلى DateTime
  static DateTime parseDateTime12Hour(String timeString, DateTime baseDate) {
    final timeOfDay = parseTime12Hour(timeString);
    return DateTime(
      baseDate.year,
      baseDate.month,
      baseDate.day,
      timeOfDay.hour,
      timeOfDay.minute,
    );
  }
  
  /// الحصول على قائمة بالأوقات المقترحة بنظام 12 ساعة
  static List<String> getSuggestedTimes() {
    return [
      '6:00 صباحاً',
      '7:00 صباحاً',
      '8:00 صباحاً',
      '9:00 صباحاً',
      '10:00 صباحاً',
      '11:00 صباحاً',
      '12:00 ظهراً',
      '1:00 مساءً',
      '2:00 مساءً',
      '3:00 مساءً',
      '4:00 مساءً',
      '5:00 مساءً',
      '6:00 مساءً',
      '7:00 مساءً',
      '8:00 مساءً',
      '9:00 مساءً',
      '10:00 مساءً',
      '11:00 مساءً',
      '12:00 منتصف الليل',
    ];
  }
}

// إضافة extension لـ TimeOfDay
extension TimeOfDayExtension on TimeOfDay {
  String to12HourFormat() {
    return TimeFormatter.formatTimeOfDay12Hour(this);
  }
}

// إضافة extension لـ DateTime
extension DateTimeExtension on DateTime {
  String to12HourFormat() {
    return TimeFormatter.formatTime12Hour(this);
  }
  
  String to12HourFormatEnglish() {
    return TimeFormatter.formatTime12HourEnglish(this);
  }
}

import 'package:balshifa/models/medicine.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/utils/dose_scheduler.dart';
import 'package:balshifa/utils/time_formatter.dart';
import 'package:balshifa/constants/app_colors.dart';
import 'package:balshifa/constants/app_styles.dart';
import 'package:flutter/material.dart';

class AddMedicineScreen extends StatefulWidget {
  final int patientId;

  const AddMedicineScreen({super.key, required this.patientId});

  @override
  State<AddMedicineScreen> createState() => _AddMedicineScreenState();
}

class _AddMedicineScreenState extends State<AddMedicineScreen> {
  final _formKey = GlobalKey<FormState>();
  
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _dosageController = TextEditingController();
  final TextEditingController _timesPerDayController = TextEditingController();
  final TextEditingController _durationController = TextEditingController();
  
  String _selectedType = 'مسكن';
  DateTime _firstDoseDateTime = DateTime.now();
  
  List<String> medicineTypes = [
    'مسكن',
    'مضاد حيوي',
    'خافض حرارة',
    'مكمل غذائي',
    'مضاد التهاب',
    'مضاد حموضة',
    'مهدئ',
    'فيتامين',
    'مضاد فيروسي',
    'مضاد فطري',
  ];

  _saveMedicine() async {
    if (_formKey.currentState!.validate()) {
      final medicine = Medicine(
        patientId: widget.patientId,
        name: _nameController.text.trim(),
        type: _selectedType,
        dosage: _dosageController.text.trim(),
        timesPerDay: int.parse(_timesPerDayController.text),
        durationDays: _durationController.text.isEmpty
            ? null
            : int.parse(_durationController.text),
        firstDoseDateTime: _firstDoseDateTime,
      );

      try {
        // حفظ الدواء
        final medicineId = await DatabaseService.instance.insertMedicine(medicine);
        
        // حفظ اسم الدواء للاستخدام المستقبلي
        await DatabaseService.instance.saveMedicineName(medicine.name);
        
        // جدولة الجرعات
        await DoseScheduler.scheduleDoses(medicine.copyWithId(medicineId));
        
        if (mounted) {
          // عرض رسالة نجاح
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حفظ الدواء "${medicine.name}" وجدولة الجرعات بنجاح! ✅'),
              backgroundColor: AppColors.success,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حفظ الدواء: $e'),
              backgroundColor: AppColors.error,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header عصري
              Container(
                width: double.infinity,
                margin: const EdgeInsets.all(20),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: AppColors.secondaryGradient,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.secondary.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'إضافة دواء جديد 💊',
                                style: TextStyle(
                                  color: AppColors.textWhite,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'أدخل تفاصيل الدواء والجرعات',
                                style: TextStyle(
                                  color: AppColors.textWhite.withOpacity(0.9),
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // النموذج
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      children: [
                        // اسم الدواء
                        _buildSectionTitle('معلومات الدواء'),
                        const SizedBox(height: 16),

                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: AppStyles.modernCard,
                          child: Column(
                            children: [
                              TextFormField(
                                controller: _nameController,
                                decoration: AppStyles.inputDecoration(
                                  'اسم الدواء',
                                  icon: Icons.medication,
                                ),
                                style: AppStyles.bodyLarge,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'يرجى إدخال اسم الدواء';
                                  }
                                  return null;
                                },
                              ),

                              const SizedBox(height: 20),

                              // نوع الدواء
                              DropdownButtonFormField<String>(
                                value: _selectedType,
                                decoration: AppStyles.inputDecoration(
                                  'نوع الدواء',
                                  icon: Icons.category,
                                ),
                                style: AppStyles.bodyLarge,
                                items: medicineTypes.map((type) {
                                  return DropdownMenuItem(
                                    value: type,
                                    child: Text(type),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedType = value;
                                    });
                                  }
                                },
                              ),

                              const SizedBox(height: 20),

                              // الجرعة
                              TextFormField(
                                controller: _dosageController,
                                decoration: AppStyles.inputDecoration(
                                  'الجرعة (مثال: قرص واحد، 5 مل)',
                                  icon: Icons.scale,
                                ),
                                style: AppStyles.bodyLarge,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'يرجى إدخال الجرعة';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // معلومات الجرعات
                        _buildSectionTitle('معلومات الجرعات'),
                        const SizedBox(height: 16),

                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: AppStyles.modernCard,
                          child: Column(
                            children: [
                              // عدد مرات الجرعة في اليوم
                              TextFormField(
                                controller: _timesPerDayController,
                                decoration: AppStyles.inputDecoration(
                                  'عدد مرات الجرعة في اليوم (1-6)',
                                  icon: Icons.repeat,
                                ),
                                style: AppStyles.bodyLarge,
                                keyboardType: TextInputType.number,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'يرجى إدخال عدد مرات الجرعة';
                                  }
                                  final times = int.tryParse(value);
                                  if (times == null || times < 1 || times > 6) {
                                    return 'يرجى إدخال رقم بين 1 و 6';
                                  }
                                  return null;
                                },
                              ),

                              const SizedBox(height: 20),

                              // مدة العلاج
                              TextFormField(
                                controller: _durationController,
                                decoration: AppStyles.inputDecoration(
                                  'مدة العلاج (بالأيام) - اختياري',
                                  icon: Icons.calendar_today,
                                ),
                                style: AppStyles.bodyLarge,
                                keyboardType: TextInputType.number,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // تاريخ ووقت أول جرعة
                        _buildSectionTitle('موعد أول جرعة'),
                        const SizedBox(height: 16),

                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: AppStyles.modernCard,
                          child: InkWell(
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: _firstDoseDateTime,
                                firstDate: DateTime(2020),
                                lastDate: DateTime(2030),
                              );

                              if (date != null) {
                                final time = await showTimePicker(
                                  context: context,
                                  initialTime: TimeOfDay.fromDateTime(_firstDoseDateTime),
                                );

                                if (time != null) {
                                  setState(() {
                                    _firstDoseDateTime = DateTime(
                                      date.year,
                                      date.month,
                                      date.day,
                                      time.hour,
                                      time.minute,
                                    );
                                  });
                                }
                              }
                            },
                            borderRadius: BorderRadius.circular(12),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: AppColors.lightBlue,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.schedule,
                                      color: AppColors.info,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'تاريخ ووقت أول جرعة',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            color: AppColors.textPrimary,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          _formatDateTime(_firstDoseDateTime),
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: AppColors.textSecondary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.edit,
                                    color: AppColors.primary,
                                    size: 20,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 32),

                        // أزرار الإجراءات
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () => Navigator.pop(context),
                                style: AppStyles.outlineButton,
                                child: const Text('إلغاء'),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 2,
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: AppColors.primaryGradient,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.primary.withOpacity(0.3),
                                      blurRadius: 15,
                                      offset: const Offset(0, 8),
                                    ),
                                  ],
                                ),
                                child: ElevatedButton.icon(
                                  onPressed: _saveMedicine,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.transparent,
                                    elevation: 0,
                                    padding: const EdgeInsets.symmetric(vertical: 16),
                                  ),
                                  icon: const Icon(
                                    Icons.save,
                                    color: Colors.white,
                                  ),
                                  label: const Text(
                                    'حفظ الدواء',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final dateStr = '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')}';
    final timeStr = TimeFormatter.formatTime12Hour(dateTime);
    return '$dateStr - $timeStr';
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _dosageController.dispose();
    _timesPerDayController.dispose();
    _durationController.dispose();
    super.dispose();
  }
}

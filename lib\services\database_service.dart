import 'package:balshifa/models/dose.dart';
import 'package:balshifa/models/medicine.dart';
import 'package:balshifa/models/patient.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseService {
  static final DatabaseService instance = DatabaseService._init();
  static Database? _db;

  DatabaseService._init();

  Future<Database> get database async {
    if (_db != null) return _db!;
    _db = await _initDB('balshifa.db');
    return _db!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(path, version: 1, onCreate: _createDB);
  }

  Future _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE patients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE medicines (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        patient_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        dosage TEXT NOT NULL,
        times_per_day INTEGER NOT NULL,
        duration_days INTEGER,
        first_dose_datetime TEXT NOT NULL,
        FOREIGN KEY (patient_id) REFERENCES patients (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE doses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        medicine_id INTEGER NOT NULL,
        scheduled_datetime TEXT NOT NULL,
        taken_datetime TEXT,
        status TEXT NOT NULL,
        FOREIGN KEY (medicine_id) REFERENCES medicines (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE saved_medicine_names (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL
      )
    ''');
  }

  // PATIENTS
  Future<int> insertPatient(Patient patient) async {
    final db = await instance.database;
    return await db.insert('patients', patient.toMap());
  }

  Future<List<Patient>> getAllPatients() async {
    final db = await instance.database;
    final result = await db.query('patients');
    return result.map((map) => Patient.fromMap(map)).toList();
  }

  Future<int> deletePatient(int id) async {
    final db = await instance.database;
    return await db.delete('patients', where: 'id = ?', whereArgs: [id]);
  }

  // MEDICINES
  Future<int> insertMedicine(Medicine medicine) async {
    final db = await instance.database;
    return await db.insert('medicines', medicine.toMap());
  }

  Future<List<Medicine>> getMedicinesByPatient(int patientId) async {
    final db = await instance.database;
    final result = await db.query(
      'medicines',
      where: 'patient_id = ?',
      whereArgs: [patientId],
    );
    return result.map((map) => Medicine.fromMap(map)).toList();
  }

  Future<int> deleteMedicine(int id) async {
    final db = await instance.database;
    return await db.delete('medicines', where: 'id = ?', whereArgs: [id]);
  }

  Future<int> updateMedicine(Medicine medicine) async {
    final db = await instance.database;
    return await db.update('medicines', medicine.toMap(),
        where: 'id = ?', whereArgs: [medicine.id]);
  }

  Future<Medicine?> getMedicineById(int id) async {
    final db = await instance.database;
    final result = await db.query(
      'medicines',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (result.isNotEmpty) {
      return Medicine.fromMap(result.first);
    }
    return null;
  }

  // DOSES
  Future<int> insertDose(Dose dose) async {
    final db = await instance.database;
    return await db.insert('doses', dose.toMap());
  }

  Future<List<Dose>> getScheduledDoses(int medicineId) async {
    final db = await instance.database;
    final result = await db.query(
      'doses',
      where: 'medicine_id = ? AND status = ?',
      whereArgs: [medicineId, 'scheduled'],
    );
    return result.map((map) => Dose.fromMap(map)).toList();
  }

  Future<int> updateDose(Dose dose) async {
    final db = await instance.database;
    return await db.update('doses', dose.toMap(),
        where: 'id = ?', whereArgs: [dose.id]);
  }

  Future<int> deleteDosesByMedicine(int medicineId) async {
    final db = await instance.database;
    return await db.delete('doses', where: 'medicine_id = ?', whereArgs: [medicineId]);
  }

  Future<List<Dose>> getDoseHistory(int patientId) async {
    final db = await instance.database;
    final result = await db.rawQuery('''
      SELECT d.* FROM doses d
      JOIN medicines m ON d.medicine_id = m.id
      WHERE m.patient_id = ?
      ORDER BY d.scheduled_datetime DESC
    ''', [patientId]);
    return result.map((map) => Dose.fromMap(map)).toList();
  }

  Future<Dose?> getDoseById(int id) async {
    final db = await instance.database;
    final result = await db.query(
      'doses',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (result.isNotEmpty) {
      return Dose.fromMap(result.first);
    }
    return null;
  }

  // SAVED MEDICINE NAMES
  Future<void> saveMedicineName(String name) async {
    final db = await instance.database;
    try {
      await db.insert('saved_medicine_names', {'name': name});
    } catch (e) {
      // الاسم موجود بالفعل
    }
  }

  Future<List<String>> getSavedMedicineNames() async {
    final db = await instance.database;
    final result = await db.query('saved_medicine_names');
    return result.map((map) => map['name'] as String).toList();
  }

  Future<List<String>> searchMedicineNames(String query) async {
    final db = await instance.database;
    final result = await db.query(
      'saved_medicine_names',
      where: 'name LIKE ?',
      whereArgs: ['$query%'],
    );
    return result.map((map) => map['name'] as String).toList();
  }
}

# دليل إصدار تطبيق بالشفا 🏥

## 📋 معلومات التطبيق

- **الاسم:** بالشفا (Balshifa)
- **الوصف:** تطبيق طبي لإدارة وجدولة مواعيد الأدوية
- **Package ID:** com.balshifa.medical
- **الإصدار:** 1.0.0 (Build 1)

## 🔧 الإعدادات المحدثة

### ✅ تم تحديث الملفات التالية:

1. **pubspec.yaml**
   - تحديث الوصف للعربية
   - إضافة مجلد assets

2. **android/app/build.gradle.kts**
   - تغيير applicationId إلى `com.balshifa.medical`
   - إضافة إعدادات ProGuard
   - تحسين إعدادات البناء
   - دعم التوقيع الآمن

3. **AndroidManifest.xml**
   - تحديث اسم التطبيق إلى "بالشفا"
   - إضافة أذونات الإشعارات
   - تحديث package name

4. **proguard-rules.pro**
   - قواعد حماية وتحسين الكود
   - الاحتفاظ بكلاسات Flutter المهمة

## 🚀 خطوات البناء

### الخطوة 1: التحضير
```bash
# تنظيف المشروع
flutter clean

# تحميل التبعيات
flutter pub get

# فحص الأخطاء
flutter analyze
```

### الخطوة 2: بناء APK للاختبار
```bash
# بناء APK عادي
flutter build apk --release

# أو بناء APK مقسم (أصغر حجماً)
flutter build apk --split-per-abi --release
```

### الخطوة 3: بناء App Bundle للنشر
```bash
flutter build appbundle --release
```

## 📁 مواقع الملفات

- **APK:** `build/app/outputs/flutter-apk/app-release.apk`
- **APK مقسم:** `build/app/outputs/flutter-apk/app-arm64-v8a-release.apk`
- **App Bundle:** `build/app/outputs/bundle/release/app-release.aab`

## 🔐 إعداد التوقيع للإنتاج

### إنشاء Keystore جديد:
```bash
keytool -genkey -v -keystore balshifa-release-key.jks \
        -keyalg RSA -keysize 2048 -validity 10000 \
        -alias balshifa
```

### إنشاء ملف key.properties:
```properties
storePassword=كلمة_مرور_قوية
keyPassword=كلمة_مرور_المفتاح
keyAlias=balshifa
storeFile=../balshifa-release-key.jks
```

## 📱 اختبار التطبيق

### الوظائف الأساسية:
- ✅ إضافة مريض جديد
- ✅ إضافة دواء للمريض
- ✅ جدولة الجرعات
- ✅ إشعارات التذكير
- ✅ تأكيد أخذ الجرعة
- ✅ عرض سجل الجرعات
- ✅ تعديل معلومات الدواء

### اختبار الإشعارات:
1. إضافة دواء بوقت قريب
2. انتظار الإشعار
3. تأكيد وصول الإشعار
4. اختبار الصوت والاهتزاز

## 🎯 الميزات المتضمنة

### إدارة البيانات:
- قاعدة بيانات SQLite محلية
- نسخ احتياطي تلقائي للبيانات
- حفظ أسماء الأدوية للاستخدام المستقبلي

### الإشعارات:
- تذكير بمواعيد الجرعات
- صوت مخصص للإشعارات
- اهتزاز عند التنبيه
- إشعارات دائمة حتى التأكيد

### واجهة المستخدم:
- تصميم عربي متجاوب
- ألوان متناسقة ومريحة
- أيقونات واضحة ومفهومة
- تجربة مستخدم سلسة

## 🔍 استكشاف الأخطاء

### مشاكل البناء:
```bash
# إذا فشل البناء
flutter clean
flutter pub get
flutter doctor

# فحص مشاكل Android
flutter doctor --android-licenses
```

### مشاكل الإشعارات:
1. التحقق من أذونات التطبيق
2. إعادة تثبيت التطبيق
3. اختبار على جهاز حقيقي

## 📊 معلومات تقنية

### المتطلبات:
- **Android:** 5.0+ (API 21)
- **RAM:** 2GB+
- **مساحة:** 50MB
- **أذونات:** الإشعارات، الاهتزاز

### التبعيات:
- Flutter SDK 3.2.3+
- sqflite 2.3.0
- flutter_local_notifications 15.1.1
- intl 0.19.0

## 🚀 خطوات النشر

### للتوزيع المباشر:
1. بناء APK مقسم
2. اختبار على أجهزة مختلفة
3. توزيع الملف

### للنشر على Google Play:
1. بناء App Bundle
2. إعداد صفحة المتجر
3. رفع الملف
4. انتظار المراجعة

## 📝 ملاحظات مهمة

- احتفظ بنسخة احتياطية من keystore
- لا تشارك ملف key.properties
- اختبر على أجهزة مختلفة
- تأكد من عمل جميع الميزات
- راجع سياسات Google Play قبل النشر

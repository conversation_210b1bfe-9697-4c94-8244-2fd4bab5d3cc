# نظام المنبه الجديد - بالشفا 🚨

## نظرة عامة

تم استبدال نظام الإشعارات القديم بنظام منبه كامل يعتمد على `AlarmManager` في Android، مما يضمن عمل التنبيهات حتى لو كان التطبيق مغلقاً أو الجهاز في وضع Doze.

## المميزات الجديدة

### 1. نظام منبه متقدم
- **AlarmManager**: يستخدم `setExactAndAllowWhileIdle` للعمل في جميع الأوضاع
- **معرف فريد**: كل جرعة لها معرف فريد لإدارة المنبهات بشكل مستقل
- **عمل في الخلفية**: يعمل حتى لو التطبيق مغلق أو الجهاز في وضع توفير البطارية

### 2. شاشة منبه كاملة
- **ملء الشاشة**: تظهر فوق جميع التطبيقات وشاشة القفل
- **تشغيل الشاشة**: يوقظ الجهاز تلقائياً عند وقت الجرعة
- **صوت مستمر**: يشغل صوت تنبيه مستمر حتى يتم الرد
- **اهتزاز**: يضيف اهتزاز للتأكد من لفت الانتباه

### 3. خيارات الاستجابة
- **تم أخذ الجرعة**: يوقف التنبيه ويسجل الجرعة كمأخوذة
- **تذكير بعد 5 دقائق**: يؤجل التنبيه لمدة 5 دقائق فقط دون تعديل الموعد الأصلي

## الملفات الجديدة

### Flutter (Dart)
- `lib/services/alarm_service.dart`: خدمة إدارة المنبهات الجديدة

### Android (Kotlin)
- `android/app/src/main/kotlin/com/example/balshifa/AlarmReceiver.kt`: مستقبل المنبهات من AlarmManager
- `android/app/src/main/kotlin/com/example/balshifa/AlarmActivity.kt`: شاشة المنبه كاملة الشاشة

## كيفية عمل النظام

### 1. جدولة المنبهات
```dart
// جدولة جميع منبهات الجرعات
await AlarmService().scheduleAllDoseAlarms();

// جدولة منبه لجرعة محددة
await AlarmService().scheduleAlarmForDose(doseId, medicineName, alarmTime);
```

### 2. تشغيل المنبه
1. `AlarmManager` يرسل Intent إلى `AlarmReceiver`
2. `AlarmReceiver` يبدأ `AlarmActivity`
3. `AlarmActivity` تعرض الشاشة الزرقاء مع الصوت والاهتزاز

### 3. الاستجابة للمنبه
- **أخذ الجرعة**: يرسل إشعار إلى Flutter عبر MethodChannel
- **التأجيل**: يجدول منبه جديد بمعرف مختلف (ID + 100000)

## الأذونات المطلوبة

```xml
<!-- أذونات المنبه -->
<uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
<uses-permission android:name="android.permission.USE_EXACT_ALARM" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.TURN_SCREEN_ON" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.DISABLE_KEYGUARD" />

<!-- أذونات الصوت والاهتزاز -->
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
```

## إعدادات AndroidManifest

### AlarmActivity
```xml
<activity
    android:name=".AlarmActivity"
    android:exported="false"
    android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
    android:launchMode="singleTop"
    android:excludeFromRecents="true"
    android:showWhenLocked="true"
    android:turnScreenOn="true"
    android:screenOrientation="portrait"
    android:noHistory="true" />
```

### AlarmReceiver
```xml
<receiver
    android:name=".AlarmReceiver"
    android:enabled="true"
    android:exported="false" />
```

## التحسينات المضافة

### 1. إدارة الذاكرة
- تنظيف MediaPlayer بعد الاستخدام
- إيقاف الاهتزاز عند إغلاق النشاط

### 2. مقاومة الأخطاء
- معالجة الأخطاء في جميع العمليات
- رسائل تشخيص مفصلة في وحدة التحكم

### 3. تجربة المستخدم
- منع إغلاق المنبه بزر الرجوع
- واجهة واضحة ومفهومة
- ألوان مميزة للأزرار

## الاختبار

### اختبار سريع
```dart
// في home_screen.dart
_testAlarm() // ينشئ منبه تجريبي بعد 5 ثوانٍ
```

### فحص المنبهات المجدولة
```dart
await AlarmService().scheduleAllDoseAlarms();
// تحقق من وحدة التحكم للرسائل التشخيصية
```

## الملفات المحذوفة

تم حذف الملفات التالية من النظام القديم:
- `AlarmForegroundService.kt`
- `BootReceiver.kt`
- `NOTIFICATION_TROUBLESHOOTING.md`

## ملاحظات مهمة

1. **معرفات التأجيل**: يتم إضافة 100000 لمعرف الجرعة عند التأجيل لتجنب التداخل
2. **عدم تعديل الموعد الأصلي**: التأجيل لا يؤثر على موعد الجرعة الأصلي في قاعدة البيانات
3. **العمل في وضع Doze**: النظام مصمم للعمل حتى في وضع توفير البطارية
4. **شاشة القفل**: المنبه يظهر فوق شاشة القفل ويوقظ الجهاز

## استكشاف الأخطاء

### إذا لم تظهر المنبهات:
1. تحقق من أذونات التطبيق في إعدادات الجهاز
2. تأكد من عدم تحسين البطارية للتطبيق
3. تحقق من وحدة التحكم للرسائل التشخيصية

### إذا لم يعمل الصوت:
1. تحقق من إعدادات الصوت في الجهاز
2. تأكد من عدم تفعيل الوضع الصامت
3. تحقق من أذونات الصوت للتطبيق

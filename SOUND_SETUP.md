# إعداد الأصوات المخصصة - تطبيق بالشفا

## 🔊 إضافة صوت الإشعارات المخصص

لتفعيل الصوت المخصص لإشعارات التذكير بالجرعات، اتبع الخطوات التالية:

### 📁 الخطوة 1: إعداد المجلد
تم إنشاء المجلد المطلوب تلقائياً في:
```
android/app/src/main/res/raw/
```

### 🎵 الخطوة 2: الحصول على ملف صوتي
يمكنك الحصول على ملف صوتي من:

**مواقع الأصوات المجانية:**
- [Freesound.org](https://freesound.org) - أصوات مجانية عالية الجودة
- [Zapsplat.com](https://zapsplat.com) - مكتبة أصوات شاملة
- [Pixabay Sounds](https://pixabay.com/sound-effects/) - أصوات مجانية

**أو استخدم هذه الأصوات الجاهزة:**
- صوت جرس لطيف
- صوت تنبيه هادئ
- صوت "بيب" قصير

### 📝 الخطوة 3: تحضير الملف
1. **اختر ملف صوتي مناسب:**
   - المدة: 1-3 ثواني
   - الصيغة: MP3 أو WAV
   - الحجم: أقل من 100KB

2. **أعد تسمية الملف إلى:**
   ```
   notification.mp3
   ```

3. **تأكد من المتطلبات:**
   - ✅ اسم الملف بالإنجليزية فقط
   - ✅ بدون مسافات أو رموز خاصة
   - ✅ صيغة صوتية مدعومة (mp3, wav, ogg)

### 📂 الخطوة 4: وضع الملف
انسخ ملف `notification.mp3` إلى:
```
android/app/src/main/res/raw/notification.mp3
```

### 🔧 الخطوة 5: إعادة البناء
بعد إضافة الملف، قم بإعادة بناء التطبيق:

```bash
# تنظيف المشروع
flutter clean

# إعادة تحميل التبعيات
flutter pub get

# بناء التطبيق
flutter build apk
```

### ✅ التحقق من النجاح
بعد إعادة البناء وتشغيل التطبيق:
1. أضف دواء جديد
2. انتظر وقت الإشعار
3. يجب أن تسمع الصوت المخصص

### 🔧 استكشاف الأخطاء

**إذا لم يعمل الصوت:**
1. تأكد من اسم الملف: `notification.mp3`
2. تأكد من المسار: `android/app/src/main/res/raw/`
3. تأكد من صيغة الملف (MP3/WAV)
4. أعد بناء التطبيق بالكامل

**إذا استمر عدم العمل:**
- سيستخدم التطبيق الصوت الافتراضي للنظام
- تأكد من تفعيل الصوت في إعدادات الجهاز
- تحقق من أذونات الإشعارات

### 📱 ملاحظات إضافية
- الصوت المخصص يعمل على أندرويد فقط
- على iOS، يستخدم النظام الصوت الافتراضي
- يمكن تغيير اسم الملف في الكود إذا أردت استخدام اسم مختلف

### 🎯 نصائح للحصول على أفضل تجربة
1. **اختر صوت هادئ وواضح** - لا يكون مزعجاً
2. **تجنب الأصوات الطويلة** - 2-3 ثواني كافية
3. **اختبر الصوت** قبل إضافته للتطبيق
4. **احفظ نسخة احتياطية** من الملف الصوتي

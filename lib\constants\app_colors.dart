import 'package:flutter/material.dart';

class AppColors {
  // الألوان الأساسية - تصميم عصري مع الأخضر كلون رئيسي
  static const Color primary = Color(0xFF00D4AA);        // أخضر فيروزي عصري
  static const Color primaryDark = Color(0xFF00B894);    // أخضر داكن
  static const Color primaryLight = Color(0xFF55EFC4);   // أخضر فاتح
  static const Color secondary = Color(0xFF6C5CE7);      // بنفسجي عصري
  static const Color accent = Color(0xFFFF6B6B);         // وردي/أحمر ناعم

  // ألوان الخلفية - تدرجات ناعمة
  static const Color background = Color(0xFFF8FAFC);     // أبيض مزرق فاتح
  static const Color surface = Color(0xFFFFFFFF);        // أبيض نقي
  static const Color cardBackground = Color(0xFFFFFFFF); // أبيض للكروت
  static const Color lightGreen = Color(0xFFE8F8F5);     // أخضر فاتح جداً
  static const Color lightBlue = Color(0xFFEBF4FF);      // أزرق فاتح جداً
  static const Color lightPurple = Color(0xFFF3F0FF);    // بنفسجي فاتح جداً

  // ألوان النص - تباين واضح
  static const Color textPrimary = Color(0xFF2D3748);    // رمادي داكن
  static const Color textSecondary = Color(0xFF718096);  // رمادي متوسط
  static const Color textHint = Color(0xFFA0AEC0);       // رمادي فاتح
  static const Color textWhite = Color(0xFFFFFFFF);      // أبيض للنص على الخلفيات الداكنة

  // ألوان الحالة - ألوان حديثة
  static const Color success = Color(0xFF48BB78);        // أخضر نجاح
  static const Color warning = Color(0xFFED8936);        // برتقالي تحذير
  static const Color error = Color(0xFFE53E3E);          // أحمر خطأ
  static const Color info = Color(0xFF4299E1);           // أزرق معلومات

  // ألوان إضافية للتصميم العصري
  static const Color divider = Color(0xFFE2E8F0);        // خط فاصل ناعم
  static const Color shadow = Color(0x0F000000);         // ظل خفيف
  static const Color overlay = Color(0x80000000);        // طبقة شفافة

  // للتوافق مع الكود القديم
  static const Color textColor = textPrimary;
  static const Color textColorSecondary = textSecondary;

  // تدرجات للخلفيات
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF00D4AA), Color(0xFF00B894)],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF6C5CE7), Color(0xFF5A4FCF)],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFFF8FAFC), Color(0xFFEDF2F7)],
  );
}

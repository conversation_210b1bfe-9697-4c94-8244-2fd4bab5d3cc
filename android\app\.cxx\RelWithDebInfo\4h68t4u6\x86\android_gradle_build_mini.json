{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\balshifa\\android\\app\\.cxx\\RelWithDebInfo\\4h68t4u6\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\balshifa\\android\\app\\.cxx\\RelWithDebInfo\\4h68t4u6\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
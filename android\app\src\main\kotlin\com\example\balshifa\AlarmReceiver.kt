package com.example.balshifa

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor

class AlarmReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        Log.d("AlarmReceiver", "تم استلام منبه!")

        val payload = intent.getStringExtra("payload") ?: ""
        Log.d("AlarmReceiver", "البيانات: $payload")

        // تحليل البيانات
        val parts = payload.split("|")
        if (parts.size >= 3) {
            val doseId = parts[0]
            val medicineName = parts[1]
            val doseTime = parts[2]

            // إظهار نشاط المنبه مع البيانات الكاملة
            val alarmIntent = Intent(context, AlarmActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_SINGLE_TOP or
                       Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT
                putExtra("dose_id", doseId)
                putExtra("medicine_name", medicineName)
                putExtra("dose_time", doseTime)
                putExtra("sound", "alarm_default")
                putExtra("payload", payload)
            }

            try {
                context.startActivity(alarmIntent)
                Log.d("AlarmReceiver", "تم بدء نشاط المنبه للدواء: $medicineName")
            } catch (e: Exception) {
                Log.e("AlarmReceiver", "خطأ في بدء نشاط المنبه: ${e.message}")

                // محاولة بديلة - إرسال broadcast للتطبيق
                try {
                    val broadcastIntent = Intent("com.example.balshifa.SHOW_ALARM").apply {
                        putExtra("payload", payload)
                    }
                    context.sendBroadcast(broadcastIntent)
                    Log.d("AlarmReceiver", "تم إرسال broadcast كبديل")
                } catch (e2: Exception) {
                    Log.e("AlarmReceiver", "خطأ في إرسال broadcast: ${e2.message}")
                }
            }
        } else {
            Log.e("AlarmReceiver", "بيانات غير صحيحة: $payload")
        }
    }
}

package com.example.balshifa

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class AlarmReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        Log.d("AlarmReceiver", "🚨 تم استلام منبه من AlarmManager!")

        val payload = intent.getStringExtra("payload") ?: ""
        Log.d("AlarmReceiver", "البيانات: $payload")

        // تحليل البيانات
        val parts = payload.split("|")
        if (parts.size >= 3) {
            val doseId = parts[0]
            val medicineName = parts[1]
            val doseTime = parts[2]

            Log.d("AlarmReceiver", "🔔 منبه للدواء: $medicineName في الوقت: $doseTime")

            // إظهار نشاط المنبه مع البيانات الكاملة
            val alarmIntent = Intent(context, AlarmActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_SINGLE_TOP or
                       Intent.FLAG_ACTIVITY_NO_HISTORY
                putExtra("dose_id", doseId)
                putExtra("medicine_name", medicineName)
                putExtra("dose_time", doseTime)
                putExtra("sound", "alarm_default")
                putExtra("payload", payload)
            }

            try {
                context.startActivity(alarmIntent)
                Log.d("AlarmReceiver", "✅ تم بدء نشاط المنبه للدواء: $medicineName")
            } catch (e: Exception) {
                Log.e("AlarmReceiver", "❌ خطأ في بدء نشاط المنبه: ${e.message}")
            }
        } else {
            Log.e("AlarmReceiver", "❌ بيانات غير صحيحة: $payload")
        }
    }
}

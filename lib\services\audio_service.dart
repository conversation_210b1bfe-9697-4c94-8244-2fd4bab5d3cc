import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  static Future<void> playNotificationSound() async {
    try {
      // هذا يتطلب إضافة ملف صوتي إلى المشروع
      // سنستخدم نظام الصوت القياسي للأندرويد
      final methodChannel = const MethodChannel('audio_service');
      await methodChannel.invokeMethod('playSound');
    } catch (e) {
      print('خطأ في تشغيل الصوت: $e');
    }
  }
}
